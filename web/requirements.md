# MediaCrawler Web监控平台需求分析

## 项目背景
基于MediaCrawler多平台社交媒体数据采集工具，构建一个专业的舆情监控和数据分析Web平台，为用户提供便捷的数据查询、筛选和分析功能。

## 核心需求分析

### 1. 界面需求

#### 1.1 整体布局
- **顶部导航栏**: 品牌标识 + 主要功能模块导航
- **左侧边栏**: 数据分类和统计展示
- **主内容区**: 数据筛选控制 + 内容列表展示
- **状态栏**: 数据统计和导出功能

#### 1.2 设计原则
- 简洁大方，适合生产环境使用
- 响应式设计，支持不同屏幕尺寸
- 专业的企业级界面风格
- 清晰的信息层次和视觉引导

### 2. 功能需求

#### 2.1 数据筛选功能（核心功能）

##### 媒体类型筛选
- **支持平台**: 
  - 小红书 (xhs_note)
  - 抖音 (douyin_aweme) 
  - 快手 (kuaishou_video)
  - 哔哩哔哩 (bilibili_video)
  - 微博 (weibo_note)
  - 贴吧 (tieba_note)
  - 知乎 (zhihu_content)
- **显示格式**: 平台名称 + 数据数量，如"小红书(134)"
- **筛选逻辑**: 支持单选和多选模式

##### 时间范围筛选
- **日期选择**: 开始时间 - 结束时间
- **快捷选项**: 今天、昨天、最近7天、最近30天
- **时间格式**: YYYY-MM-DD HH:mm
- **默认范围**: 最近7天

##### 情感分析筛选
- **情感分类**: 正面、负面、中性、全部
- **数据来源**: 基于analysis_info字段中的sentiment字段
- **显示样式**: 不同情感使用不同颜色标识

##### 内容搜索功能
- **搜索范围**: 标题、内容、关键词
- **搜索类型**: 精确匹配、模糊匹配
- **高级搜索**: 支持逻辑运算符（AND、OR、NOT）

#### 2.2 数据展示功能

##### 列表展示
- **基本信息**: 标题、内容摘要、发布时间、平台来源
- **数据指标**: 点赞数、评论数、转发数、浏览数
- **情感分析**: 情感倾向、情感评分
- **分页控制**: 支持分页浏览，每页20-50条记录

##### 排序功能
- **时间排序**: 发布时间倒序/正序
- **热度排序**: 根据互动数据排序
- **相关度排序**: 根据搜索关键词相关度排序

#### 2.3 统计信息功能

##### 实时统计
- **总数据量**: 各平台数据总数统计
- **时间分布**: 选定时间范围内的数据分布
- **情感分布**: 正面、负面、中性内容占比
- **平台分布**: 各平台数据量占比

### 3. 数据结构需求

#### 3.1 数据库表映射
```
平台表映射关系:
- xhs_note -> 小红书
- douyin_aweme -> 抖音  
- kuaishou_video -> 快手
- bilibili_video -> 哔哩哔哩
- weibo_note -> 微博
- tieba_note -> 贴吧
- zhihu_content -> 知乎
```

#### 3.2 核心字段
```
公共字段:
- id: 主键ID
- title: 标题
- desc/content: 内容描述
- publish_time: 发布时间
- add_ts: 采集时间
- liked_count: 点赞数
- comments_count: 评论数
- shared_count: 分享数
- analysis_info: AI分析结果(JSON格式)

平台特殊字段:
- note_id/aweme_id/video_id: 平台内容ID
- author_id: 作者ID
- author_name: 作者名称
```

#### 3.3 分析数据结构
```json
analysis_info字段结构:
{
  "sentiment": "positive/negative/neutral",
  "sentiment_score": 0.85,
  "summary": "内容摘要",
  "keywords": ["关键词1", "关键词2"],
  "category": "分类标签",
  "relevance_score": 0.92
}
```

### 4. 性能需求

#### 4.1 响应性能
- **查询响应**: 普通查询 < 2秒，复杂查询 < 5秒
- **页面加载**: 首次加载 < 3秒，后续操作 < 1秒
- **并发支持**: 支持10-50个并发用户

#### 4.2 数据处理
- **分页大小**: 默认20条，最大100条
- **缓存策略**: 查询结果缓存5分钟
- **数据更新**: 支持实时数据刷新

### 5. 安全需求

#### 5.1 数据安全
- **只读访问**: Web界面仅提供数据查询，不允许修改
- **SQL注入防护**: 使用ORM框架防止SQL注入
- **敏感信息**: 不显示用户手机号、邮箱等敏感信息

#### 5.2 访问控制
- **本地访问**: 默认仅允许本地访问
- **权限管理**: 后续可扩展用户权限管理

### 6. 技术约束

#### 6.1 技术栈限制
- **后端**: Python + SQLAlchemy ORM
- **前端**: Streamlit框架
- **数据库**: MySQL（复用现有数据库）
- **部署**: 本地部署，支持生产环境

#### 6.2 兼容性要求
- **浏览器**: Chrome、Firefox、Safari、Edge最新版本
- **屏幕**: 支持1920x1080及以上分辨率
- **移动端**: 基本的移动端适配

### 7. 扩展性考虑

#### 7.1 功能扩展
- **报告生成**: 支持PDF、Excel格式报告导出
- **数据可视化**: 图表展示和趋势分析
- **告警功能**: 关键词监控和实时告警
- **API接口**: 提供REST API供第三方调用

#### 7.2 数据扩展
- **新平台接入**: 支持新增社交媒体平台
- **分析维度**: 增加更多AI分析维度
- **历史数据**: 支持历史数据归档和查询

## 实施优先级

### MVP功能（第一阶段）
1. 基础数据查询和展示
2. 媒体类型筛选
3. 时间范围筛选
4. 关键词搜索
5. 简单的分页功能

### 增强功能（第二阶段）
1. 情感分析筛选
2. 高级搜索功能
3. 排序和统计
4. 界面美化和优化

### 高级功能（第三阶段）
1. 数据导出功能
2. 实时数据更新
3. 用户权限管理
4. 性能优化和缓存

## 成功标准

### 功能完整性
- ✅ 支持所有7个主要平台的数据查询
- ✅ 筛选功能准确有效
- ✅ 搜索结果相关度高
- ✅ 界面操作流畅直观

### 性能指标
- ✅ 查询响应时间达标
- ✅ 支持预期并发用户数
- ✅ 内存和CPU使用合理
- ✅ 界面加载速度快

### 用户体验
- ✅ 界面简洁专业
- ✅ 操作逻辑清晰
- ✅ 错误提示友好
- ✅ 数据展示准确