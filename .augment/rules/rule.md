---
type: "always_apply"
---

# 协作工作规范

## 1. 代码实施流程
- **方案讨论阶段**：当我提出需求时，请先进行以下分析，不要直接编写代码：
  - 需求技术可行性评估
  - 提出2-3种可行的技术方案及其优缺点对比
  - 详细说明推荐方案的实施步骤和优先级
  - 明确指出潜在风险和注意事项
  - 等待我的明确确认后再进行下一步

- **代码编写阶段**：仅在收到以下明确指令时才开始编写代码：
  - "请开始实施"
  - "请编写代码"
  - "请执行"
  - 其他明确表示可以开始编码的指令

## 2. 测试代码规范
- 除非我明确要求编写测试，否则不要创建任何测试文件或测试函数
- 当需要测试Python代码时，优先使用`python -c`内联执行，避免创建额外文件
- 测试代码应当简洁明了，仅验证关键功能点

## 3. 代码风格与安全
- **命令执行安全**：对于`rm`、`drop`等危险命令，必须谨慎执行：
  - 明确指出潜在风险
  - 建议使用更安全的替代方案

- **Java代码风格**：
  - 在编写Java代码前，先分析项目现有代码风格（缩进、命名规范、注释风格等）
  - 严格遵循项目已有的代码组织结构和设计模式
  - 确保新代码与项目整体风格保持一致

## 4. 沟通规范
- 所有回复和讨论使用中文
- 技术术语可使用通用英文表达
- 代码注释根据项目实际情况选择中文或英文
- 我的Java项目注释用中文，log用英文，其它的项目没有明确指定一般都用中文即可

## 5. 环境简介
- 我使用IDEA开发Java，vscode开发python
- Java使用windows环境编译，其它语言基本上在wsl2环境中执行
- 你运行的环境一般都是在wsl2中

## 6. 工作记录自动化
- 当用户提及"记录工作"、"添加工作记录"、"记一下工作"等关键词，或明确要求记录工作内容时，或执行了较大规模的工程时，自动执行以下操作：
  1. 使用 mcp__lark-mcp__bitable_v1_appTableRecord_create API
  2. 目标：app_token="YpXHba1tWaeK1es8FGRcfLx5nBh", table_id="tblZg7HNWrZZh3Ef"
  3. 字段填充：
     - "记录时间": 一定要先使用终端命令 date +%s%3N 获取毫秒时间戳并填写"
     - "工作内容": 用户描述内容或刚刚做的内容
     - "类型": 自动判断（开发/修复/会议/学习/其他/TODO 除此之外你也可以自己发挥新增类型）
     - "备注": 自动判断
  4. 确认记录成功后简要回复
  5. wiki位置：space_id="7502385734939574291"的工作记录表

## 7. 技术调研
- 当要求进行技术调研时，可以通过多个数据源比如web search，gh命令方式调研
- 如果使用gh进行调研时，建议综合考虑项目更新时间，star数量，issue评论反馈来判断当前库是否可用
- 反馈给用户时，附带相关网页链接或关键词