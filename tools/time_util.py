# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：  
# 1. 不得用于任何商业用途。  
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。  
# 3. 不得进行大规模爬取或对平台造成运营干扰。  
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。   
# 5. 不得用于任何非法或不当的用途。
#   
# 详细许可条款请参阅项目根目录下的LICENSE文件。  
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。  


# -*- coding: utf-8 -*-
# <AUTHOR> <EMAIL>
# @Time    : 2023/12/2 12:52
# @Desc    : 时间相关的工具函数

import time
from datetime import datetime, timedelta, timezone


def get_current_timestamp() -> int:
    """
    获取当前的时间戳(13 位)：1701493264496
    :return:
    """
    return int(time.time() * 1000)


def get_current_time() -> str:
    """
    获取当前的时间：'2023-12-02 13:01:23'
    :return:
    """
    return time.strftime('%Y-%m-%d %X', time.localtime())


def get_current_date() -> str:
    """
    获取当前的日期：'2023-12-02'
    :return:
    """
    return time.strftime('%Y-%m-%d', time.localtime())


def get_time_str_from_unix_time(unixtime):
    """
    unix 整数类型时间戳  ==> 字符串日期时间
    :param unixtime:
    :return:
    """
    if int(unixtime) > 1000000000000:
        unixtime = int(unixtime) / 1000
    return time.strftime('%Y-%m-%d %X', time.localtime(unixtime))


def get_date_str_from_unix_time(unixtime):
    """
    unix 整数类型时间戳  ==> 字符串日期
    :param unixtime:
    :return:
    """
    if int(unixtime) > 1000000000000:
        unixtime = int(unixtime) / 1000
    return time.strftime('%Y-%m-%d', time.localtime(unixtime))


def get_unix_time_from_time_str(time_str):
    """
    字符串时间 ==> unix 整数类型时间戳，精确到秒
    :param time_str:
    :return:
    """
    try:
        format_str = "%Y-%m-%d %H:%M:%S"
        tm_object = time.strptime(str(time_str), format_str)
        return int(time.mktime(tm_object))
    except Exception as e:
        return 0
    pass


def get_unix_timestamp():
    return int(time.time())


def rfc2822_to_china_datetime(rfc2822_time):
    try:
        # 定义RFC 2822格式
        rfc2822_format = "%a %b %d %H:%M:%S %z %Y"

        # 将RFC 2822时间字符串转换为datetime对象
        dt_object = datetime.strptime(rfc2822_time, rfc2822_format)

        # 将datetime对象的时区转换为中国时区
        dt_object_china = dt_object.astimezone(timezone(timedelta(hours=8)))
        return dt_object_china
    except ValueError:
        # 处理简短时间格式，如 '01-26'、'昨天'、'今天' 等
        try:
            # 如果是 MM-DD 格式
            if len(rfc2822_time) == 5 and '-' in rfc2822_time:
                current_year = datetime.now().year
                month_day = rfc2822_time.split('-')
                month = int(month_day[0])
                day = int(month_day[1])
                dt_object = datetime(current_year, month, day, 0, 0, 0)
                # 转换为中国时区
                dt_object_china = dt_object.replace(tzinfo=timezone(timedelta(hours=8)))
                return dt_object_china
            # 如果是其他格式，返回当前时间
            else:
                return datetime.now(timezone(timedelta(hours=8)))
        except Exception:
            # 解析失败，返回当前时间
            return datetime.now(timezone(timedelta(hours=8)))


def rfc2822_to_timestamp(rfc2822_time):
    try:
        # 定义RFC 2822格式
        rfc2822_format = "%a %b %d %H:%M:%S %z %Y"

        # 将RFC 2822时间字符串转换为datetime对象
        dt_object = datetime.strptime(rfc2822_time, rfc2822_format)

        # 将datetime对象转换为UTC时间
        dt_utc = dt_object.replace(tzinfo=timezone.utc)

        # 计算UTC时间对应的Unix时间戳
        timestamp = int(dt_utc.timestamp())

        return timestamp
    except ValueError:
        # 处理简短时间格式，如 '01-26'、'昨天'、'今天' 等
        try:
            # 如果是 MM-DD 格式
            if len(rfc2822_time) == 5 and '-' in rfc2822_time:
                current_year = datetime.now().year
                month_day = rfc2822_time.split('-')
                month = int(month_day[0])
                day = int(month_day[1])
                dt_object = datetime(current_year, month, day, 0, 0, 0)
                return int(dt_object.timestamp())
            # 如果是其他格式，返回当前时间戳
            else:
                return int(time.time())
        except Exception:
            # 解析失败，返回当前时间戳
            return int(time.time())


if __name__ == '__main__':
    # 示例用法
    _rfc2822_time = "Sat Dec 23 17:12:54 +0800 2023"
    print(rfc2822_to_china_datetime(_rfc2822_time))
