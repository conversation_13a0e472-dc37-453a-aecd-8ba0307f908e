{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(cat:*)", "Bash(apt list:*)", "Bash(gh issue:*)", "Bash(ss:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(chmod:*)", "Bash(gh repo view:*)", "Bash(git remote add:*)", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(claude mcp:*)", "mcp__lark-mcp__docx_builtin_search", "mcp__lark-mcp__docx_v1_document_rawContent", "Bash(npx @larksuiteoapi/lark-mcp mcp:*)", "mcp__lark-mcp__wiki_v1_node_search", "mcp__lark-mcp__wiki_v2_space_getNode", "mcp__lark-mcp__docx_builtin_import", "mcp__lark-mcp__wiki_v2_spaceNode_moveDocsToWiki", "mcp__lark-mcp__bitable_v1_appTableRecord_create", "mcp__lark-mcp__wiki_v2_spaceNode_list", "mcp__lark-mcp__bitable_v1_app_create", "mcp__lark-mcp__bitable_v1_appTable_create", "mcp__lark-mcp__bitable_v1_appTableField_list", "Bash(./scripts/run.sh:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(timeout:*)", "Bash(grep:*)", "Bash(gh search repos:*)", "Bash(gh api:*)", "Bash(ls:*)", "Bash(gh search issues:*)", "WebFetch(domain:tieba.baidu.com)", "mcp__chrome-mcp-server__chrome_navigate", "mcp__chrome-mcp-server__chrome_get_web_content", "mcp__chrome-mcp-server__chrome_get_interactive_elements", "Bash(streamlit run:*)", "Bash(./run.sh)", "Bash(pip3 list:*)", "mcp__chrome-mcp-server__chrome_screenshot", "mcp__chrome-mcp-server__chrome_fill_or_select", "mcp__chrome-mcp-server__chrome_click_element", "mcp__chrome-mcp-server__chrome_keyboard", "mcp__chrome-mcp-server__get_windows_and_tabs"], "deny": []}}