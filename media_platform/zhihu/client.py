# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：  
# 1. 不得用于任何商业用途。  
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。  
# 3. 不得进行大规模爬取或对平台造成运营干扰。  
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。   
# 5. 不得用于任何非法或不当的用途。
#   
# 详细许可条款请参阅项目根目录下的LICENSE文件。  
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。  


# -*- coding: utf-8 -*-
import asyncio
import json
from typing import Any, Callable, Dict, List, Optional, Union
from urllib.parse import urlencode

import httpx
from httpx import Response
from playwright.async_api import BrowserContext, Page
from tenacity import retry, stop_after_attempt, wait_fixed

import config
from base.base_crawler import AbstractApiClient
from constant import zhihu as zhihu_constant
from model.m_zhihu import <PERSON><PERSON><PERSON><PERSON>om<PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, ZhihuCreator
from tools import utils

from .exception import DataFetchError, ForbiddenError
from .field import SearchSort, SearchTime, SearchType
from .help import ZhihuExtractor, sign


class ZhiHuClient(AbstractApiClient):
    def __init__(
            self,
            timeout=10,
            proxies=None,
            *,
            headers: Dict[str, str],
            playwright_page: Page,
            cookie_dict: Dict[str, str],
    ):
        self.proxies = proxies
        self.timeout = timeout
        self.default_headers = headers
        self.cookie_dict = cookie_dict
        self._extractor = ZhihuExtractor()
        
        # 代理降级相关状态
        self._proxy_failed = False  # 代理失败标记
        self._proxy_retry_count = 0  # 代理重试计数
        self._max_proxy_retries = 3  # 最大代理重试次数

    async def _pre_headers(self, url: str) -> Dict:
        """
        请求头参数签名
        Args:
            url:  请求的URL需要包含请求的参数
        Returns:

        """
        d_c0 = self.cookie_dict.get("d_c0")
        if not d_c0:
            raise Exception("d_c0 not found in cookies")
        sign_res = sign(url, self.default_headers["cookie"])
        headers = self.default_headers.copy()
        headers['x-zst-81'] = sign_res["x-zst-81"]
        headers['x-zse-96'] = sign_res["x-zse-96"]
        return headers

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1))
    async def request(self, method, url, **kwargs) -> Union[str, Any]:
        """
        封装httpx的公共请求方法，对请求响应做一些处理
        Args:
            method: 请求方法
            url: 请求的URL
            **kwargs: 其他请求参数，例如请求头、请求体等

        Returns:

        """
        # return response.text
        return_response = kwargs.pop('return_response', False)

        # 处理代理参数
        proxy = None
        use_proxy = False
        
        # 如果代理未失败且配置了代理，则使用代理
        if not self._proxy_failed and self.proxies:
            if isinstance(self.proxies, dict):
                proxy = list(self.proxies.values())[0] if self.proxies else None
            else:
                proxy = self.proxies
            use_proxy = True
        
        # 尝试请求
        try:
            async with httpx.AsyncClient(proxy=proxy) as client:
                response = await client.request(
                    method, url, timeout=self.timeout,
                    **kwargs
                )
            
            # 请求成功，重置代理重试计数
            if use_proxy:
                self._proxy_retry_count = 0

            if response.status_code != 200:
                utils.logger.error(f"[ZhiHuClient.request] Requset Url: {url}, Request error: {response.text}")
                if response.status_code == 403:
                    raise ForbiddenError(response.text)
                elif response.status_code == 404: # 如果一个content没有评论也是404
                    return {}

                raise DataFetchError(response.text)

            if return_response:
                return response.text
            try:
                data: Dict = response.json()
                if data.get("error"):
                    utils.logger.error(f"[ZhiHuClient.request] Request error: {data}")
                    raise DataFetchError(data.get("error", {}).get("message"))
                return data
            except json.JSONDecodeError:
                utils.logger.error(f"[ZhiHuClient.request] Request error: {response.text}")
                raise DataFetchError(response.text)
                
        except (httpx.ProxyError, httpx.ConnectError, httpx.TimeoutException) as e:
            # 代理相关错误处理
            if use_proxy:
                self._proxy_retry_count += 1
                utils.logger.warning(f"[ZhiHuClient] 代理请求失败 ({self._proxy_retry_count}/{self._max_proxy_retries}): {e}")
                
                # 如果重试次数未达到上限，直接重试
                if self._proxy_retry_count < self._max_proxy_retries:
                    utils.logger.info(f"[ZhiHuClient] 重试代理请求: {url}")
                    return await self.request(method, url, **kwargs)
                else:
                    # 达到重试上限，标记代理失败并降级到无代理模式
                    self._proxy_failed = True
                    utils.logger.error(f"[ZhiHuClient] 代理连续失败{self._max_proxy_retries}次，降级到无代理模式")
                    utils.logger.info(f"[ZhiHuClient] 使用无代理模式重试请求: {url}")
                    return await self.request(method, url, **kwargs)
            else:
                # 无代理模式下的连接错误，直接抛出
                utils.logger.error(f"[ZhiHuClient] 无代理模式下请求失败: {e}")
                raise e
    
    def reset_proxy_status(self):
        """重置代理状态，允许重新尝试使用代理"""
        self._proxy_failed = False
        self._proxy_retry_count = 0
        utils.logger.info("[ZhiHuClient] 代理状态已重置")
    
    def get_proxy_status(self) -> Dict[str, Any]:
        """获取当前代理状态信息"""
        return {
            "proxy_failed": self._proxy_failed,
            "proxy_retry_count": self._proxy_retry_count,
            "max_proxy_retries": self._max_proxy_retries,
            "current_mode": "无代理模式" if self._proxy_failed else "代理模式"
        }


    async def get(self, uri: str, params=None, **kwargs) -> Union[Response, Dict, str]:
        """
        GET请求，对请求头签名
        Args:
            uri: 请求路由
            params: 请求参数

        Returns:

        """
        final_uri = uri
        if isinstance(params, dict):
            final_uri += '?' + urlencode(params)
        headers = await self._pre_headers(final_uri)
        base_url = (
            zhihu_constant.ZHIHU_URL
            if "/p/" not in uri
            else zhihu_constant.ZHIHU_ZHUANLAN_URL
        )
        return await self.request(method="GET", url=base_url + final_uri, headers=headers, **kwargs)

    async def pong(self) -> bool:
        """
        用于检查登录态是否失效了
        Returns:

        """
        utils.logger.info("[ZhiHuClient.pong] Begin to pong zhihu...")
        ping_flag = False
        try:
            res = await self.get_current_user_info()
            if res.get("uid") and res.get("name"):
                ping_flag = True
                utils.logger.info("[ZhiHuClient.pong] Ping zhihu successfully")
            else:
                utils.logger.error(f"[ZhiHuClient.pong] Ping zhihu failed, response data: {res}")
        except Exception as e:
            utils.logger.error(f"[ZhiHuClient.pong] Ping zhihu failed: {e}, and try to login again...")
            ping_flag = False
        return ping_flag

    async def update_cookies(self, browser_context: BrowserContext):
        """
        API客户端提供的更新cookies方法，一般情况下登录成功后会调用此方法
        Args:
            browser_context: 浏览器上下文对象

        Returns:

        """
        cookie_str, cookie_dict = utils.convert_cookies(await browser_context.cookies())
        self.default_headers["cookie"] = cookie_str
        self.cookie_dict = cookie_dict

    async def get_current_user_info(self) -> Dict:
        """
        获取当前登录用户信息
        Returns:

        """
        params = {
            "include": "email,is_active,is_bind_phone"
        }
        return await self.get("/api/v4/me", params)

    async def get_note_by_keyword(
            self, keyword: str,
            page: int = 1,
            page_size: int = 20,
            sort: SearchSort = SearchSort.DEFAULT,
            note_type: SearchType = SearchType.DEFAULT,
            search_time: SearchTime = SearchTime.DEFAULT
    ) -> List[ZhihuContent]:
        """
        根据关键词搜索
        Args:
            keyword: 关键词
            page: 第几页
            page_size: 分页size
            sort: 排序
            note_type: 搜索结果类型
            search_time: 搜索多久时间的结果

        Returns:

        """
        uri = "/api/v4/search_v3"
        params = {
            "gk_version": "gz-gaokao",
            "t": "general",
            "q": keyword,
            "correction": 1,
            "offset": (page - 1) * page_size,
            "limit": page_size,
            "filter_fields": "",
            "lc_idx": (page - 1) * page_size,
            "show_all_topics": 0,
            "search_source": "Filter",
            "time_interval": search_time.value,
            "sort": sort.value,
            "vertical": note_type.value,
        }
        search_res = await self.get(uri, params)
        utils.logger.info(f"[ZhiHuClient.get_note_by_keyword] Search result: {search_res}")
        return self._extractor.extract_contents_from_search(search_res)

    async def get_root_comments(self, content_id: str, content_type: str, offset: str = "", limit: int = 10,
                                order_by: str = "score") -> Dict:
        """
        获取内容的一级评论
        Args:
            content_id: 内容ID
            content_type: 内容类型(answer, article, zvideo)
            offset:
            limit:
            order_by:

        Returns:

        """
        uri = f"/api/v4/comment_v5/{content_type}s/{content_id}/root_comment"
        params = {"order": order_by, "offset": offset, "limit": limit}
        return await self.get(uri, params)
        # uri = f"/api/v4/{content_type}s/{content_id}/root_comments"
        # params = {
        #     "order": order_by,
        #     "offset": offset,
        #     "limit": limit
        # }
        # return await self.get(uri, params)

    async def get_child_comments(self, root_comment_id: str, offset: str = "", limit: int = 10,
                                 order_by: str = "sort") -> Dict:
        """
        获取一级评论下的子评论
        Args:
            root_comment_id:
            offset:
            limit:
            order_by:

        Returns:

        """
        uri = f"/api/v4/comment_v5/comment/{root_comment_id}/child_comment"
        params = {
            "order": order_by,
            "offset": offset,
            "limit": limit
        }
        return await self.get(uri, params)

    async def get_note_all_comments(self, content: ZhihuContent, crawl_interval: float = 1.0,
                                    callback: Optional[Callable] = None) -> List[ZhihuComment]:
        """
        获取指定帖子下的所有一级评论，该方法会一直查找一个帖子下的所有评论信息
        Args:
            content: 内容详情对象(问题｜文章｜视频)
            crawl_interval: 爬取一次笔记的延迟单位（秒）
            callback: 一次笔记爬取结束后

        Returns:

        """
        result: List[ZhihuComment] = []
        is_end: bool = False
        offset: str = ""
        limit: int = 10
        
        # 添加循环保护机制
        max_pages = 25  # 最大页数限制
        page_count = 0
        empty_result_count = 0  # 连续空结果计数
        seen_offsets = set()  # 记录已处理的offset，防止重复
        
        while not is_end and page_count < max_pages:
            page_count += 1
            utils.logger.info(f"[ZhihuClient.get_note_all_comments] Getting comments for {content.content_id}, page {page_count}, offset: {offset}")
            
            try:
                root_comment_res = await self.get_root_comments(content.content_id, content.content_type, offset, limit)
                if not root_comment_res:
                    empty_result_count += 1
                    utils.logger.warning(f"[ZhihuClient.get_note_all_comments] Empty response for {content.content_id}, count: {empty_result_count}")
                    if empty_result_count >= 3:  # 连续3次空结果则退出
                        utils.logger.error(f"[ZhihuClient.get_note_all_comments] Too many empty responses, breaking loop")
                        break
                    continue
                
                paging_info = root_comment_res.get("paging", {})
                is_end = paging_info.get("is_end", True)  # 默认为True，更安全
                new_offset = self._extractor.extract_offset(paging_info)
                
                # 检查offset重复
                if new_offset and new_offset in seen_offsets:
                    utils.logger.warning(f"[ZhihuClient.get_note_all_comments] Duplicate offset detected: {new_offset}, breaking loop")
                    break
                
                if new_offset:
                    seen_offsets.add(new_offset)
                    offset = new_offset
                
                comments = self._extractor.extract_comments(content, root_comment_res.get("data"))

                if not comments:
                    empty_result_count += 1
                    utils.logger.warning(f"[ZhihuClient.get_note_all_comments] No comments extracted for {content.content_id}, count: {empty_result_count}")
                    if empty_result_count >= 3:  # 连续3次无评论则退出
                        utils.logger.error(f"[ZhihuClient.get_note_all_comments] Too many empty comment extractions, breaking loop")
                        break
                    continue
                
                # 重置空结果计数
                empty_result_count = 0

                if callback:
                    await callback(comments)

                result.extend(comments)
                await self.get_comments_all_sub_comments(content, comments, crawl_interval=crawl_interval, callback=callback)
                await asyncio.sleep(crawl_interval)
                
            except Exception as ex:
                utils.logger.error(f"[ZhihuClient.get_note_all_comments] Error getting comments for {content.content_id}: {ex}")
                break
                
        utils.logger.info(f"[ZhihuClient.get_note_all_comments] Finished getting comments for {content.content_id}, total pages: {page_count}, total comments: {len(result)}")
        return result

    async def get_comments_all_sub_comments(self, content: ZhihuContent, comments: List[ZhihuComment], crawl_interval: float = 1.0,
                                            callback: Optional[Callable] = None) -> List[ZhihuComment]:
        """
        获取指定评论下的所有子评论
        Args:
            content: 内容详情对象(问题｜文章｜视频)
            comments: 评论列表
            crawl_interval: 爬取一次笔记的延迟单位（秒）
            callback: 一次笔记爬取结束后

        Returns:

        """
        if not config.ENABLE_GET_SUB_COMMENTS:
            return []

        all_sub_comments: List[ZhihuComment] = []
        for parment_comment in comments:
            if parment_comment.sub_comment_count == 0:
                continue

            # 添加子评论循环保护机制
            is_end: bool = False
            offset: str = ""
            limit: int = 10
            max_sub_pages = 50  # 每个评论最大子评论页数
            sub_page_count = 0
            empty_result_count = 0
            seen_sub_offsets = set()  # 记录已处理的子评论offset
            
            utils.logger.info(f"[ZhihuClient.get_comments_all_sub_comments] Getting sub comments for comment {parment_comment.comment_id}, expected count: {parment_comment.sub_comment_count}")
            
            while not is_end and sub_page_count < max_sub_pages:
                sub_page_count += 1
                
                try:
                    child_comment_res = await self.get_child_comments(parment_comment.comment_id, offset, limit)
                    if not child_comment_res:
                        empty_result_count += 1
                        utils.logger.warning(f"[ZhihuClient.get_comments_all_sub_comments] Empty response for comment {parment_comment.comment_id}, count: {empty_result_count}")
                        if empty_result_count >= 3:
                            utils.logger.error(f"[ZhihuClient.get_comments_all_sub_comments] Too many empty responses, breaking loop")
                            break
                        continue
                        
                    paging_info = child_comment_res.get("paging", {})
                    is_end = paging_info.get("is_end", True)  # 默认为True，更安全
                    new_offset = self._extractor.extract_offset(paging_info)
                    
                    # 检查子评论offset重复
                    if new_offset and new_offset in seen_sub_offsets:
                        utils.logger.warning(f"[ZhihuClient.get_comments_all_sub_comments] Duplicate sub offset detected: {new_offset}, breaking loop")
                        break
                    
                    if new_offset:
                        seen_sub_offsets.add(new_offset)
                        offset = new_offset
                    
                    sub_comments = self._extractor.extract_comments(content, child_comment_res.get("data"))

                    if not sub_comments:
                        empty_result_count += 1
                        utils.logger.warning(f"[ZhihuClient.get_comments_all_sub_comments] No sub comments extracted for comment {parment_comment.comment_id}, count: {empty_result_count}")
                        if empty_result_count >= 3:
                            utils.logger.error(f"[ZhihuClient.get_comments_all_sub_comments] Too many empty sub comment extractions, breaking loop")
                            break
                        continue
                    
                    # 重置空结果计数
                    empty_result_count = 0

                    if callback:
                        await callback(sub_comments)

                    all_sub_comments.extend(sub_comments)
                    await asyncio.sleep(crawl_interval)
                    
                except Exception as ex:
                    utils.logger.error(f"[ZhihuClient.get_comments_all_sub_comments] Error getting sub comments for comment {parment_comment.comment_id}: {ex}")
                    break
                    
            utils.logger.info(f"[ZhihuClient.get_comments_all_sub_comments] Finished getting sub comments for comment {parment_comment.comment_id}, pages: {sub_page_count}")
            
        return all_sub_comments

    async def get_creator_info(self, url_token: str) -> Optional[ZhihuCreator]:
        """
        获取创作者信息
        Args:
            url_token:

        Returns:

        """
        uri = f"/people/{url_token}"
        html_content: str = await self.get(uri, return_response=True)
        return self._extractor.extract_creator(url_token, html_content)

    async def get_creator_answers(self, url_token: str, offset: int = 0, limit: int = 20) -> Dict:
        """
        获取创作者的回答
        Args:
            url_token:
            offset:
            limit:

        Returns:


        """
        uri = f"/api/v4/members/{url_token}/answers"
        params = {
            "include":"data[*].is_normal,admin_closed_comment,reward_info,is_collapsed,annotation_action,annotation_detail,collapse_reason,collapsed_by,suggest_edit,comment_count,can_comment,content,editable_content,attachment,voteup_count,reshipment_settings,comment_permission,created_time,updated_time,review_info,excerpt,paid_info,reaction_instruction,is_labeled,label_info,relationship.is_authorized,voting,is_author,is_thanked,is_nothelp;data[*].vessay_info;data[*].author.badge[?(type=best_answerer)].topics;data[*].author.vip_info;data[*].question.has_publishing_draft,relationship",
            "offset": offset,
            "limit": limit,
            "order_by": "created"
        }
        return await self.get(uri, params)

    async def get_creator_articles(self, url_token: str, offset: int = 0, limit: int = 20) -> Dict:
        """
        获取创作者的文章
        Args:
            url_token:
            offset:
            limit:

        Returns:

        """
        uri = f"/api/v4/members/{url_token}/articles"
        params = {
            "include":"data[*].comment_count,suggest_edit,is_normal,thumbnail_extra_info,thumbnail,can_comment,comment_permission,admin_closed_comment,content,voteup_count,created,updated,upvoted_followees,voting,review_info,reaction_instruction,is_labeled,label_info;data[*].vessay_info;data[*].author.badge[?(type=best_answerer)].topics;data[*].author.vip_info;",
            "offset": offset,
            "limit": limit,
            "order_by": "created"
        }
        return await self.get(uri, params)

    async def get_creator_videos(self, url_token: str, offset: int = 0, limit: int = 20) -> Dict:
        """
        获取创作者的视频
        Args:
            url_token:
            offset:
            limit:

        Returns:

        """
        uri = f"/api/v4/members/{url_token}/zvideos"
        params = {
            "include":"similar_zvideo,creation_relationship,reaction_instruction",
            "offset": offset,
            "limit": limit,
            "similar_aggregation": "true"
        }
        return await self.get(uri, params)

    async def get_all_anwser_by_creator(self, creator: ZhihuCreator, crawl_interval: float = 1.0,
                                        callback: Optional[Callable] = None) -> List[ZhihuContent]:
        """
        获取创作者的所有回答
        Args:
            creator: 创作者信息
            crawl_interval: 爬取一次笔记的延迟单位（秒）
            callback: 一次笔记爬取结束后

        Returns:

        """
        all_contents: List[ZhihuContent] = []
        is_end: bool = False
        offset: int = 0
        limit: int = 20
        while not is_end:
            res = await self.get_creator_answers(creator.url_token, offset, limit)
            if not res:
                break
            utils.logger.info(f"[ZhiHuClient.get_all_anwser_by_creator] Get creator {creator.url_token} answers: {res}")
            paging_info = res.get("paging", {})
            is_end = paging_info.get("is_end")
            contents = self._extractor.extract_content_list_from_creator(res.get("data"))
            if callback:
                await callback(contents)
            all_contents.extend(contents)
            offset += limit
            await asyncio.sleep(crawl_interval)
        return all_contents


    async def get_all_articles_by_creator(self, creator: ZhihuCreator, crawl_interval: float = 1.0,
                                          callback: Optional[Callable] = None) -> List[ZhihuContent]:
        """
        获取创作者的所有文章
        Args:
            creator:
            crawl_interval:
            callback:

        Returns:

        """
        all_contents: List[ZhihuContent] = []
        is_end: bool = False
        offset: int = 0
        limit: int = 20
        while not is_end:
            res = await self.get_creator_articles(creator.url_token, offset, limit)
            if not res:
                break
            paging_info = res.get("paging", {})
            is_end = paging_info.get("is_end")
            contents = self._extractor.extract_content_list_from_creator(res.get("data"))
            if callback:
                await callback(contents)
            all_contents.extend(contents)
            offset += limit
            await asyncio.sleep(crawl_interval)
        return all_contents


    async def get_all_videos_by_creator(self, creator: ZhihuCreator, crawl_interval: float = 1.0,
                                        callback: Optional[Callable] = None) -> List[ZhihuContent]:
        """
        获取创作者的所有视频
        Args:
            creator:
            crawl_interval:
            callback:

        Returns:

        """
        all_contents: List[ZhihuContent] = []
        is_end: bool = False
        offset: int = 0
        limit: int = 20
        while not is_end:
            res = await self.get_creator_videos(creator.url_token, offset, limit)
            if not res:
                break
            paging_info = res.get("paging", {})
            is_end = paging_info.get("is_end")
            contents = self._extractor.extract_content_list_from_creator(res.get("data"))
            if callback:
                await callback(contents)
            all_contents.extend(contents)
            offset += limit
            await asyncio.sleep(crawl_interval)
        return all_contents


    async def get_answer_info(
        self, question_id: str, answer_id: str
    ) -> Optional[ZhihuContent]:
        """
        获取回答信息
        Args:
            question_id:
            answer_id:

        Returns:

        """
        uri = f"/question/{question_id}/answer/{answer_id}"
        response_html = await self.get(uri, return_response=True)
        return self._extractor.extract_answer_content_from_html(response_html)

    async def get_article_info(self, article_id: str) -> Optional[ZhihuContent]:
        """
        获取文章信息
        Args:
            article_id:

        Returns:

        """
        uri = f"/p/{article_id}"
        response_html = await self.get(uri, return_response=True)
        return self._extractor.extract_article_content_from_html(response_html)

    async def get_video_info(self, video_id: str) -> Optional[ZhihuContent]:
        """
        获取视频信息
        Args:
            video_id:

        Returns:

        """
        uri = f"/zvideo/{video_id}"
        response_html = await self.get(uri, return_response=True)
        return self._extractor.extract_zvideo_content_from_html(response_html)
